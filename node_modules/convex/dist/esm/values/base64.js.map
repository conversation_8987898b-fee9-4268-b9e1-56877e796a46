{"version": 3, "sources": ["../../../src/values/base64.ts"], "sourcesContent": ["/*\nhttps://github.com/beatgammit/base64-js/blob/88957c9943c7e2a0f03cdf73e71d579e433627d3/index.js\nCopyright (c) 2014 Jameson Little\nThe MIT License (MIT)\n*/\n\n// Vendored because this library has no ESM build, and some environments\n// (SvelteKit) are happiest when all dependencies are ESM.\n\nvar lookup: string[] = [];\nvar revLookup: number[] = [];\nvar Arr = Uint8Array;\n\nvar code = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/\";\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i];\n  revLookup[code.charCodeAt(i)] = i;\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup[\"-\".charCodeAt(0)] = 62;\nrevLookup[\"_\".charCodeAt(0)] = 63;\n\nfunction getLens(b64: string) {\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error(\"Invalid string. Length must be a multiple of 4\");\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf(\"=\");\n  if (validLen === -1) validLen = len;\n\n  var placeHoldersLen = validLen === len ? 0 : 4 - (validLen % 4);\n\n  return [validLen, placeHoldersLen];\n}\n\n// base64 is 4/3 + up to two characters of the original data\n/** @public */\nexport function byteLength(b64: string): number {\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\nfunction _byteLength(_b64: string, validLen: number, placeHoldersLen: number) {\n  return ((validLen + placeHoldersLen) * 3) / 4 - placeHoldersLen;\n}\n\n/** @public */\nexport function toByteArray(b64: string): Uint8Array {\n  var tmp;\n  var lens = getLens(b64);\n  var validLen = lens[0];\n  var placeHoldersLen = lens[1];\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen));\n\n  var curByte = 0;\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0 ? validLen - 4 : validLen;\n\n  var i;\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)];\n    arr[curByte++] = (tmp >> 16) & 0xff;\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[curByte++] = (tmp >> 8) & 0xff;\n    arr[curByte++] = tmp & 0xff;\n  }\n\n  return arr;\n}\n\nfunction tripletToBase64(num: number) {\n  return (\n    lookup[(num >> 18) & 0x3f] +\n    lookup[(num >> 12) & 0x3f] +\n    lookup[(num >> 6) & 0x3f] +\n    lookup[num & 0x3f]\n  );\n}\n\nfunction encodeChunk(uint8: Uint8Array, start: number, end: number) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xff0000) +\n      ((uint8[i + 1] << 8) & 0xff00) +\n      (uint8[i + 2] & 0xff);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join(\"\");\n}\n\n/** @public */\nexport function fromByteArray(uint8: Uint8Array): string {\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(\n      encodeChunk(\n        uint8,\n        i,\n        i + maxChunkLength > len2 ? len2 : i + maxChunkLength,\n      ),\n    );\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    parts.push(lookup[tmp >> 2] + lookup[(tmp << 4) & 0x3f] + \"==\");\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1];\n    parts.push(\n      lookup[tmp >> 10] +\n        lookup[(tmp >> 4) & 0x3f] +\n        lookup[(tmp << 2) & 0x3f] +\n        \"=\",\n    );\n  }\n\n  return parts.join(\"\");\n}\n"], "mappings": ";AASA,IAAI,SAAmB,CAAC;AACxB,IAAI,YAAsB,CAAC;AAC3B,IAAI,MAAM;AAEV,IAAI,OAAO;AACX,SAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,SAAO,CAAC,IAAI,KAAK,CAAC;AAClB,YAAU,KAAK,WAAW,CAAC,CAAC,IAAI;AAClC;AAIA,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,UAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAE/B,SAAS,QAAQ,KAAa;AAC5B,MAAI,MAAM,IAAI;AAEd,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,MAAM,gDAAgD;AAAA,EAClE;AAIA,MAAI,WAAW,IAAI,QAAQ,GAAG;AAC9B,MAAI,aAAa,GAAI,YAAW;AAEhC,MAAI,kBAAkB,aAAa,MAAM,IAAI,IAAK,WAAW;AAE7D,SAAO,CAAC,UAAU,eAAe;AACnC;AAIO,gBAAS,WAAW,KAAqB;AAC9C,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAC5B,UAAS,WAAW,mBAAmB,IAAK,IAAI;AAClD;AAEA,SAAS,YAAY,MAAc,UAAkB,iBAAyB;AAC5E,UAAS,WAAW,mBAAmB,IAAK,IAAI;AAClD;AAGO,gBAAS,YAAY,KAAyB;AACnD,MAAI;AACJ,MAAI,OAAO,QAAQ,GAAG;AACtB,MAAI,WAAW,KAAK,CAAC;AACrB,MAAI,kBAAkB,KAAK,CAAC;AAE5B,MAAI,MAAM,IAAI,IAAI,YAAY,KAAK,UAAU,eAAe,CAAC;AAE7D,MAAI,UAAU;AAGd,MAAI,MAAM,kBAAkB,IAAI,WAAW,IAAI;AAE/C,MAAI;AACJ,OAAK,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC3B,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,KACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACrC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC;AACjC,QAAI,SAAS,IAAK,OAAO,KAAM;AAC/B,QAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,QAAI,SAAS,IAAI,MAAM;AAAA,EACzB;AAEA,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,IAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,QAAI,SAAS,IAAI,MAAM;AAAA,EACzB;AAEA,MAAI,oBAAoB,GAAG;AACzB,UACG,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAChC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IACpC,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACvC,QAAI,SAAS,IAAK,OAAO,IAAK;AAC9B,QAAI,SAAS,IAAI,MAAM;AAAA,EACzB;AAEA,SAAO;AACT;AAEA,SAAS,gBAAgB,KAAa;AACpC,SACE,OAAQ,OAAO,KAAM,EAAI,IACzB,OAAQ,OAAO,KAAM,EAAI,IACzB,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAO,MAAM,EAAI;AAErB;AAEA,SAAS,YAAY,OAAmB,OAAe,KAAa;AAClE,MAAI;AACJ,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACnC,WACI,MAAM,CAAC,KAAK,KAAM,aAClB,MAAM,IAAI,CAAC,KAAK,IAAK,UACtB,MAAM,IAAI,CAAC,IAAI;AAClB,WAAO,KAAK,gBAAgB,GAAG,CAAC;AAAA,EAClC;AACA,SAAO,OAAO,KAAK,EAAE;AACvB;AAGO,gBAAS,cAAc,OAA2B;AACvD,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,aAAa,MAAM;AACvB,MAAI,QAAQ,CAAC;AACb,MAAI,iBAAiB;AAGrB,WAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,gBAAgB;AACtE,UAAM;AAAA,MACJ;AAAA,QACE;AAAA,QACA;AAAA,QACA,IAAI,iBAAiB,OAAO,OAAO,IAAI;AAAA,MACzC;AAAA,IACF;AAAA,EACF;AAGA,MAAI,eAAe,GAAG;AACpB,UAAM,MAAM,MAAM,CAAC;AACnB,UAAM,KAAK,OAAO,OAAO,CAAC,IAAI,OAAQ,OAAO,IAAK,EAAI,IAAI,IAAI;AAAA,EAChE,WAAW,eAAe,GAAG;AAC3B,WAAO,MAAM,MAAM,CAAC,KAAK,KAAK,MAAM,MAAM,CAAC;AAC3C,UAAM;AAAA,MACJ,OAAO,OAAO,EAAE,IACd,OAAQ,OAAO,IAAK,EAAI,IACxB,OAAQ,OAAO,IAAK,EAAI,IACxB;AAAA,IACJ;AAAA,EACF;AAEA,SAAO,MAAM,KAAK,EAAE;AACtB;", "names": []}