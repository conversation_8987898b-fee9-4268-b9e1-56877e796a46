{"version": 3, "sources": ["../../../../src/server/impl/actions_impl.ts"], "sourcesContent": ["import { convexTo<PERSON>son, jsonToConvex, Value } from \"../../values/index.js\";\nimport { version } from \"../../index.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\nimport { parseArgs } from \"../../common/index.js\";\nimport { FunctionReference } from \"../../server/api.js\";\nimport { getFunctionAddress } from \"../components/paths.js\";\n\nfunction syscallArgs(\n  requestId: string,\n  functionReference: any,\n  args?: Record<string, Value>,\n) {\n  const address = getFunctionAddress(functionReference);\n  return {\n    ...address,\n    args: convexToJson(parseArgs(args)),\n    version,\n    requestId,\n  };\n}\n\nexport function setupActionCalls(requestId: string) {\n  return {\n    runQuery: async (\n      query: FunctionReference<\"query\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/query\",\n        syscallArgs(requestId, query, args),\n      );\n      return jsonToConvex(result);\n    },\n    runMutation: async (\n      mutation: FunctionReference<\"mutation\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/mutation\",\n        syscallArgs(requestId, mutation, args),\n      );\n      return jsonToConvex(result);\n    },\n    runAction: async (\n      action: FunctionReference<\"action\", \"public\" | \"internal\">,\n      args?: Record<string, Value>,\n    ): Promise<any> => {\n      const result = await performAsyncSyscall(\n        \"1.0/actions/action\",\n        syscallArgs(requestId, action, args),\n      );\n      return jsonToConvex(result);\n    },\n  };\n}\n"], "mappings": ";AAAA,SAAS,cAAc,oBAA2B;AAClD,SAAS,eAAe;AACxB,SAAS,2BAA2B;AACpC,SAAS,iBAAiB;AAE1B,SAAS,0BAA0B;AAEnC,SAAS,YACP,WACA,mBACA,MACA;AACA,QAAM,UAAU,mBAAmB,iBAAiB;AACpD,SAAO;AAAA,IACL,GAAG;AAAA,IACH,MAAM,aAAa,UAAU,IAAI,CAAC;AAAA,IAClC;AAAA,IACA;AAAA,EACF;AACF;AAEO,gBAAS,iBAAiB,WAAmB;AAClD,SAAO;AAAA,IACL,UAAU,OACR,OACA,SACiB;AACjB,YAAM,SAAS,MAAM;AAAA,QACnB;AAAA,QACA,YAAY,WAAW,OAAO,IAAI;AAAA,MACpC;AACA,aAAO,aAAa,MAAM;AAAA,IAC5B;AAAA,IACA,aAAa,OACX,UACA,SACiB;AACjB,YAAM,SAAS,MAAM;AAAA,QACnB;AAAA,QACA,YAAY,WAAW,UAAU,IAAI;AAAA,MACvC;AACA,aAAO,aAAa,MAAM;AAAA,IAC5B;AAAA,IACA,WAAW,OACT,QACA,SACiB;AACjB,YAAM,SAAS,MAAM;AAAA,QACnB;AAAA,QACA,YAAY,WAAW,QAAQ,IAAI;AAAA,MACrC;AACA,aAAO,aAAa,MAAM;AAAA,IAC5B;AAAA,EACF;AACF;", "names": []}