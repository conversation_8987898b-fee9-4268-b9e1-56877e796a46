{"version": 3, "sources": ["../../../../src/server/impl/authentication_impl.ts"], "sourcesContent": ["import { Auth } from \"../authentication.js\";\nimport { performAsyncSyscall } from \"./syscall.js\";\n\nexport function setupAuth(requestId: string): Auth {\n  return {\n    getUserIdentity: async () => {\n      return await performAsyncSyscall(\"1.0/getUserIdentity\", {\n        requestId,\n      });\n    },\n  };\n}\n"], "mappings": ";AACA,SAAS,2BAA2B;AAE7B,gBAAS,UAAU,WAAyB;AACjD,SAAO;AAAA,IACL,iBAAiB,YAAY;AAC3B,aAAO,MAAM,oBAAoB,uBAAuB;AAAA,QACtD;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF;", "names": []}