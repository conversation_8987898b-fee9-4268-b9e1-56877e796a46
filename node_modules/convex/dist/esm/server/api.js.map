{"version": 3, "sources": ["../../../src/server/api.ts"], "sourcesContent": ["import {\n  EmptyObject,\n  DefaultFunctionArgs,\n  FunctionVisibility,\n  RegisteredAction,\n  RegisteredMutation,\n  RegisteredQuery,\n} from \"./registration.js\";\nimport { Expand, UnionToIntersection } from \"../type_utils.js\";\nimport { PaginationOptions, PaginationResult } from \"./pagination.js\";\nimport { functionName } from \"./functionName.js\";\nimport { getFunctionAddress } from \"./components/paths.js\";\n\n/**\n * The type of a Convex function.\n *\n * @public\n */\nexport type FunctionType = \"query\" | \"mutation\" | \"action\";\n\n/**\n * A reference to a registered Convex function.\n *\n * You can create a {@link FunctionReference} using the generated `api` utility:\n * ```js\n * import { api } from \"../convex/_generated/api\";\n *\n * const reference = api.myModule.myFunction;\n * ```\n *\n * If you aren't using code generation, you can create references using\n * {@link anyApi}:\n * ```js\n * import { anyApi } from \"convex/server\";\n *\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * Function references can be used to invoke functions from the client. For\n * example, in React you can pass references to the {@link react.useQuery} hook:\n * ```js\n * const result = useQuery(api.myModule.myFunction);\n * ```\n *\n * @typeParam Type - The type of the function (\"query\", \"mutation\", or \"action\").\n * @typeParam Visibility - The visibility of the function (\"public\" or \"internal\").\n * @typeParam Args - The arguments to this function. This is an object mapping\n * argument names to their types.\n * @typeParam ReturnType - The return type of this function.\n * @public\n */\nexport type FunctionReference<\n  Type extends FunctionType,\n  Visibility extends FunctionVisibility = \"public\",\n  Args extends DefaultFunctionArgs = any,\n  ReturnType = any,\n  ComponentPath = string | undefined,\n> = {\n  _type: Type;\n  _visibility: Visibility;\n  _args: Args;\n  _returnType: ReturnType;\n  _componentPath: ComponentPath;\n};\n\n/**\n * Get the name of a function from a {@link FunctionReference}.\n *\n * The name is a string like \"myDir/myModule:myFunction\". If the exported name\n * of the function is `\"default\"`, the function name is omitted\n * (e.g. \"myDir/myModule\").\n *\n * @param functionReference - A {@link FunctionReference} to get the name of.\n * @returns A string of the function's name.\n *\n * @public\n */\nexport function getFunctionName(\n  functionReference: AnyFunctionReference,\n): string {\n  const address = getFunctionAddress(functionReference);\n\n  if (address.name === undefined) {\n    if (address.functionHandle !== undefined) {\n      throw new Error(\n        `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received function handle ${address.functionHandle}`,\n      );\n    } else if (address.reference !== undefined) {\n      throw new Error(\n        `Expected function reference in the current component like \"api.file.func\" or \"internal.file.func\", but received reference ${address.reference}`,\n      );\n    }\n    throw new Error(\n      `Expected function reference like \"api.file.func\" or \"internal.file.func\", but received ${JSON.stringify(address)}`,\n    );\n  }\n  // Both a legacy thing and also a convenience for interactive use:\n  // the types won't check but a string is always allowed at runtime.\n  if (typeof functionReference === \"string\") return functionReference;\n\n  // Two different runtime values for FunctionReference implement this\n  // interface: api objects returned from `createApi()` and standalone\n  // function reference objects returned from makeFunctionReference.\n  const name = (functionReference as any)[functionName];\n  if (!name) {\n    throw new Error(`${functionReference as any} is not a functionReference`);\n  }\n  return name;\n}\n\n/**\n * FunctionReferences generally come from generated code, but in custom clients\n * it may be useful to be able to build one manually.\n *\n * Real function references are empty objects at runtime, but the same interface\n * can be implemented with an object for tests and clients which don't use\n * code generation.\n *\n * @param name - The identifier of the function. E.g. `path/to/file:functionName`\n * @public\n */\nexport function makeFunctionReference<\n  type extends FunctionType,\n  args extends DefaultFunctionArgs = any,\n  ret = any,\n>(name: string): FunctionReference<type, \"public\", args, ret> {\n  return { [functionName]: name } as unknown as FunctionReference<\n    type,\n    \"public\",\n    args,\n    ret\n  >;\n}\n\n/**\n * Create a runtime API object that implements {@link AnyApi}.\n *\n * This allows accessing any path regardless of what directories, modules,\n * or functions are defined.\n *\n * @param pathParts - The path to the current node in the API.\n * @returns An {@link AnyApi}\n * @public\n */\nfunction createApi(pathParts: string[] = []): AnyApi {\n  const handler: ProxyHandler<object> = {\n    get(_, prop: string | symbol) {\n      if (typeof prop === \"string\") {\n        const newParts = [...pathParts, prop];\n        return createApi(newParts);\n      } else if (prop === functionName) {\n        if (pathParts.length < 2) {\n          const found = [\"api\", ...pathParts].join(\".\");\n          throw new Error(\n            `API path is expected to be of the form \\`api.moduleName.functionName\\`. Found: \\`${found}\\``,\n          );\n        }\n        const path = pathParts.slice(0, -1).join(\"/\");\n        const exportName = pathParts[pathParts.length - 1];\n        if (exportName === \"default\") {\n          return path;\n        } else {\n          return path + \":\" + exportName;\n        }\n      } else if (prop === Symbol.toStringTag) {\n        return \"FunctionReference\";\n      } else {\n        return undefined;\n      }\n    },\n  };\n\n  return new Proxy({}, handler);\n}\n\n/**\n * Given an export from a module, convert it to a {@link FunctionReference}\n * if it is a Convex function.\n */\nexport type FunctionReferenceFromExport<Export> =\n  Export extends RegisteredQuery<\n    infer Visibility,\n    infer Args,\n    infer ReturnValue\n  >\n    ? FunctionReference<\n        \"query\",\n        Visibility,\n        Args,\n        ConvertReturnType<ReturnValue>\n      >\n    : Export extends RegisteredMutation<\n          infer Visibility,\n          infer Args,\n          infer ReturnValue\n        >\n      ? FunctionReference<\n          \"mutation\",\n          Visibility,\n          Args,\n          ConvertReturnType<ReturnValue>\n        >\n      : Export extends RegisteredAction<\n            infer Visibility,\n            infer Args,\n            infer ReturnValue\n          >\n        ? FunctionReference<\n            \"action\",\n            Visibility,\n            Args,\n            ConvertReturnType<ReturnValue>\n          >\n        : never;\n\n/**\n * Given a module, convert all the Convex functions into\n * {@link FunctionReference}s and remove the other exports.\n *\n * BE CAREFUL WHEN EDITING THIS!\n *\n * This is written carefully to preserve jumping to function definitions using\n * cmd+click. If you edit it, please test that cmd+click still works.\n */\ntype FunctionReferencesInModule<Module extends Record<string, any>> = {\n  -readonly [ExportName in keyof Module as Module[ExportName][\"isConvexFunction\"] extends true\n    ? ExportName\n    : never]: FunctionReferenceFromExport<Module[ExportName]>;\n};\n\n/**\n * Given a path to a module and it's type, generate an API type for this module.\n *\n * This is a nested object according to the module's path.\n */\ntype ApiForModule<\n  ModulePath extends string,\n  Module extends object,\n> = ModulePath extends `${infer First}/${infer Second}`\n  ? {\n      [_ in First]: ApiForModule<Second, Module>;\n    }\n  : { [_ in ModulePath]: FunctionReferencesInModule<Module> };\n\n/**\n * Given the types of all modules in the `convex/` directory, construct the type\n * of `api`.\n *\n * `api` is a utility for constructing {@link FunctionReference}s.\n *\n * @typeParam AllModules - A type mapping module paths (like `\"dir/myModule\"`) to\n * the types of the modules.\n * @public\n */\nexport type ApiFromModules<AllModules extends Record<string, object>> =\n  FilterApi<\n    ApiFromModulesAllowEmptyNodes<AllModules>,\n    FunctionReference<any, any, any, any>\n  >;\n\ntype ApiFromModulesAllowEmptyNodes<AllModules extends Record<string, object>> =\n  ExpandModulesAndDirs<\n    UnionToIntersection<\n      {\n        [ModulePath in keyof AllModules]: ApiForModule<\n          ModulePath & string,\n          AllModules[ModulePath]\n        >;\n      }[keyof AllModules]\n    >\n  >;\n\n/**\n * @public\n *\n * Filter a Convex deployment api object for functions which meet criteria,\n * for example all public queries.\n */\nexport type FilterApi<API, Predicate> = Expand<{\n  [mod in keyof API as API[mod] extends Predicate\n    ? mod\n    : API[mod] extends FunctionReference<any, any, any, any>\n      ? never\n      : FilterApi<API[mod], Predicate> extends Record<string, never>\n        ? never\n        : mod]: API[mod] extends Predicate\n    ? API[mod]\n    : FilterApi<API[mod], Predicate>;\n}>;\n\n/**\n * Given an api of type API and a FunctionReference subtype, return an api object\n * containing only the function references that match.\n *\n * ```ts\n * const q = filterApi<typeof api, FunctionReference<\"query\">>(api)\n * ```\n *\n * @public\n */\nexport function filterApi<API, Predicate>(api: API): FilterApi<API, Predicate> {\n  return api as any;\n}\n\n// These just* API filter helpers require no type parameters so are useable from JavaScript.\n/** @public */\nexport function justInternal<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"internal\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPublic<API>(\n  api: API,\n): FilterApi<API, FunctionReference<any, \"public\", any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justQueries<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"query\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justMutations<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justActions<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"action\", any, any, any>> {\n  return api as any;\n}\n\n/** @public */\nexport function justPaginatedQueries<API>(\n  api: API,\n): FilterApi<\n  API,\n  FunctionReference<\n    \"query\",\n    any,\n    { paginationOpts: PaginationOptions },\n    PaginationResult<any>\n  >\n> {\n  return api as any;\n}\n\n/** @public */\nexport function justSchedulable<API>(\n  api: API,\n): FilterApi<API, FunctionReference<\"mutation\" | \"action\", any, any, any>> {\n  return api as any;\n}\n\n/**\n * Like {@link Expand}, this simplifies how TypeScript displays object types.\n * The differences are:\n * 1. This version is recursive.\n * 2. This stops recursing when it hits a {@link FunctionReference}.\n */\ntype ExpandModulesAndDirs<ObjectType> = ObjectType extends AnyFunctionReference\n  ? ObjectType\n  : {\n      [Key in keyof ObjectType]: ExpandModulesAndDirs<ObjectType[Key]>;\n    };\n\n/**\n * A {@link FunctionReference} of any type and any visibility with any\n * arguments and any return type.\n *\n * @public\n */\nexport type AnyFunctionReference = FunctionReference<any, any>;\n\ntype AnyModuleDirOrFunc = {\n  [key: string]: AnyModuleDirOrFunc;\n} & AnyFunctionReference;\n\n/**\n * The type that Convex api objects extend. If you were writing an api from\n * scratch it should extend this type.\n *\n * @public\n */\nexport type AnyApi = Record<string, Record<string, AnyModuleDirOrFunc>>;\n\n/**\n * Recursive partial API, useful for defining a subset of an API when mocking\n * or building custom api objects.\n *\n * @public\n */\nexport type PartialApi<API> = {\n  [mod in keyof API]?: API[mod] extends FunctionReference<any, any, any, any>\n    ? API[mod]\n    : PartialApi<API[mod]>;\n};\n\n/**\n * A utility for constructing {@link FunctionReference}s in projects that\n * are not using code generation.\n *\n * You can create a reference to a function like:\n * ```js\n * const reference = anyApi.myModule.myFunction;\n * ```\n *\n * This supports accessing any path regardless of what directories and modules\n * are in your project. All function references are typed as\n * {@link AnyFunctionReference}.\n *\n *\n * If you're using code generation, use `api` from `convex/_generated/api`\n * instead. It will be more type-safe and produce better auto-complete\n * in your editor.\n *\n * @public\n */\nexport const anyApi: AnyApi = createApi() as any;\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * This is represented as an object mapping argument names to values.\n * @public\n */\nexport type FunctionArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`.\n *\n * This type is used to make methods involving arguments type safe while allowing\n * skipping the arguments for functions that don't require arguments.\n *\n * @public\n */\nexport type OptionalRestArgs<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_args\"] extends EmptyObject\n    ? [args?: EmptyObject]\n    : [args: FuncRef[\"_args\"]];\n\n/**\n * A tuple type of the (maybe optional) arguments to `FuncRef`, followed by an options\n * object of type `Options`.\n *\n * This type is used to make methods like `useQuery` type-safe while allowing\n * 1. Skipping arguments for functions that don't require arguments.\n * 2. Skipping the options object.\n * @public\n */\nexport type ArgsAndOptions<\n  FuncRef extends AnyFunctionReference,\n  Options,\n> = FuncRef[\"_args\"] extends EmptyObject\n  ? [args?: EmptyObject, options?: Options]\n  : [args: FuncRef[\"_args\"], options?: Options];\n\n/**\n * Given a {@link FunctionReference}, get the return type of the function.\n *\n * @public\n */\nexport type FunctionReturnType<FuncRef extends AnyFunctionReference> =\n  FuncRef[\"_returnType\"];\n\ntype UndefinedToNull<T> = T extends void ? null : T;\n\ntype NullToUndefinedOrNull<T> = T extends null ? T | undefined | void : T;\n\n/**\n * Convert the return type of a function to it's client-facing format.\n *\n * This means:\n * - Converting `undefined` and `void` to `null`\n * - Removing all `Promise` wrappers\n */\nexport type ConvertReturnType<T> = UndefinedToNull<Awaited<T>>;\n\nexport type ValidatorTypeToReturnType<T> =\n  | Promise<NullToUndefinedOrNull<T>>\n  | NullToUndefinedOrNull<T>;\n"], "mappings": ";AAUA,SAAS,oBAAoB;AAC7B,SAAS,0BAA0B;AAkE5B,gBAAS,gBACd,mBACQ;AACR,QAAM,UAAU,mBAAmB,iBAAiB;AAEpD,MAAI,QAAQ,SAAS,QAAW;AAC9B,QAAI,QAAQ,mBAAmB,QAAW;AACxC,YAAM,IAAI;AAAA,QACR,0GAA0G,QAAQ,cAAc;AAAA,MAClI;AAAA,IACF,WAAW,QAAQ,cAAc,QAAW;AAC1C,YAAM,IAAI;AAAA,QACR,6HAA6H,QAAQ,SAAS;AAAA,MAChJ;AAAA,IACF;AACA,UAAM,IAAI;AAAA,MACR,0FAA0F,KAAK,UAAU,OAAO,CAAC;AAAA,IACnH;AAAA,EACF;AAGA,MAAI,OAAO,sBAAsB,SAAU,QAAO;AAKlD,QAAM,OAAQ,kBAA0B,YAAY;AACpD,MAAI,CAAC,MAAM;AACT,UAAM,IAAI,MAAM,GAAG,iBAAwB,6BAA6B;AAAA,EAC1E;AACA,SAAO;AACT;AAaO,gBAAS,sBAId,MAA4D;AAC5D,SAAO,EAAE,CAAC,YAAY,GAAG,KAAK;AAMhC;AAYA,SAAS,UAAU,YAAsB,CAAC,GAAW;AACnD,QAAM,UAAgC;AAAA,IACpC,IAAI,GAAG,MAAuB;AAC5B,UAAI,OAAO,SAAS,UAAU;AAC5B,cAAM,WAAW,CAAC,GAAG,WAAW,IAAI;AACpC,eAAO,UAAU,QAAQ;AAAA,MAC3B,WAAW,SAAS,cAAc;AAChC,YAAI,UAAU,SAAS,GAAG;AACxB,gBAAM,QAAQ,CAAC,OAAO,GAAG,SAAS,EAAE,KAAK,GAAG;AAC5C,gBAAM,IAAI;AAAA,YACR,oFAAoF,KAAK;AAAA,UAC3F;AAAA,QACF;AACA,cAAM,OAAO,UAAU,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG;AAC5C,cAAM,aAAa,UAAU,UAAU,SAAS,CAAC;AACjD,YAAI,eAAe,WAAW;AAC5B,iBAAO;AAAA,QACT,OAAO;AACL,iBAAO,OAAO,MAAM;AAAA,QACtB;AAAA,MACF,WAAW,SAAS,OAAO,aAAa;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AAEA,SAAO,IAAI,MAAM,CAAC,GAAG,OAAO;AAC9B;AA+HO,gBAAS,UAA0B,KAAqC;AAC7E,SAAO;AACT;AAIO,gBAAS,aACd,KAC8D;AAC9D,SAAO;AACT;AAGO,gBAAS,WACd,KAC4D;AAC5D,SAAO;AACT;AAGO,gBAAS,YACd,KAC2D;AAC3D,SAAO;AACT;AAGO,gBAAS,cACd,KAC8D;AAC9D,SAAO;AACT;AAGO,gBAAS,YACd,KAC4D;AAC5D,SAAO;AACT;AAGO,gBAAS,qBACd,KASA;AACA,SAAO;AACT;AAGO,gBAAS,gBACd,KACyE;AACzE,SAAO;AACT;AAkEO,aAAM,SAAiB,UAAU;", "names": []}