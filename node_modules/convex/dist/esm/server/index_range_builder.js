"use strict";
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
export class IndexRange {
  /**
   * @internal
   */
  constructor() {
    // Property for nominal type support.
    __publicField(this, "_isIndexRange");
  }
}
//# sourceMappingURL=index_range_builder.js.map
