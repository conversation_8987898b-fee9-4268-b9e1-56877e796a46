{"version": 3, "sources": ["../../../src/server/pagination.ts"], "sourcesContent": ["import { v } from \"../values/validator.js\";\n\n/**\n * An opaque identifier used for paginating a database query.\n *\n * Cursors are returned from {@link OrderedQuery.paginate} and represent the\n * point of the query where the page of results ended.\n *\n * To continue paginating, pass the cursor back into\n * {@link OrderedQuery.paginate} in the {@link PaginationOptions} object to\n * fetch another page of results.\n *\n * Note: Cursors can only be passed to _exactly_ the same database query that\n * they were generated from. You may not reuse a cursor between different\n * database queries.\n *\n * @public\n */\nexport type Cursor = string;\n\n/**\n * The result of paginating using {@link OrderedQuery.paginate}.\n *\n * @public\n */\nexport interface PaginationResult<T> {\n  /**\n   * The page of results.\n   */\n  page: T[];\n\n  /**\n   * Have we reached the end of the results?\n   */\n  isDone: boolean;\n\n  /**\n   * A {@link Cursor} to continue loading more results.\n   */\n  continueCursor: Cursor;\n\n  /**\n   * A {@link Cursor} to split the page into two, so the page from\n   * (cursor, continueCursor] can be replaced by two pages (cursor, splitCursor]\n   * and (splitCursor, continueCursor].\n   */\n  splitCursor?: Cursor | null;\n\n  /**\n   * When a query reads too much data, it may return 'SplitRecommended' to\n   * indicate that the page should be split into two with `splitCursor`.\n   * When a query reads so much data that `page` might be incomplete, its status\n   * becomes 'SplitRequired'.\n   */\n  pageStatus?: \"SplitRecommended\" | \"SplitRequired\" | null;\n}\n\n/**\n * The options passed to {@link OrderedQuery.paginate}.\n *\n * To use this type in [argument validation](https://docs.convex.dev/functions/validation),\n * use the {@link paginationOptsValidator}.\n *\n * @public\n */\nexport interface PaginationOptions {\n  /**\n   * Number of items to load in this page of results.\n   *\n   * Note: This is only an initial value!\n   *\n   * If you are running this paginated query in a reactive query function, you\n   * may receive more or less items than this if items were added to or removed\n   * from the query range.\n   */\n  numItems: number;\n\n  /**\n   * A {@link Cursor} representing the start of this page or `null` to start\n   * at the beginning of the query results.\n   */\n  cursor: Cursor | null;\n\n  /**\n   * A {@link Cursor} representing the end of this page or `null | undefined` to\n   * use `numItems` instead.\n   *\n   * @internal\n   */\n  endCursor?: Cursor | null;\n\n  /**\n   * The maximum number of rows that should be read from the database.\n   *\n   * This option is different from `numItems` in that it controls the number of rows entering a query's\n   * pipeline, where `numItems` controls the number of rows coming out. For example, a `filter`\n   * may disqualify most of the rows coming in, so setting a low `numItems` would not help\n   * bound its execution time. Instead, set a low `maximumRowsRead` to efficiently paginate\n   * through the filter.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumRowsRead?: number;\n\n  /**\n   * The maximum number of bytes that should be read from the database.\n   *\n   * As with {@link PaginationOptions.maximumRowsRead}, this affects the number\n   * of rows entering a query's pipeline.\n   *\n   * Once a paginated query hits its bytes read budget, an incomplete page\n   * will be returned.\n   *\n   * Currently this is not enforced for search queries.\n   *\n   * @internal\n   */\n  maximumBytesRead?: number;\n}\n\n/**\n * A {@link values.Validator} for {@link PaginationOptions}.\n *\n * This includes the standard {@link PaginationOptions} properties along with\n * an optional cache-busting `id` property used by {@link react.usePaginatedQuery}.\n *\n * @public\n */\nexport const paginationOptsValidator = v.object({\n  numItems: v.number(),\n  cursor: v.union(v.string(), v.null()),\n  endCursor: v.optional(v.union(v.string(), v.null())),\n  id: v.optional(v.number()),\n  maximumRowsRead: v.optional(v.number()),\n  maximumBytesRead: v.optional(v.number()),\n});\n"], "mappings": ";AAAA,SAAS,SAAS;AAkIX,aAAM,0BAA0B,EAAE,OAAO;AAAA,EAC9C,UAAU,EAAE,OAAO;AAAA,EACnB,QAAQ,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC;AAAA,EACpC,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,KAAK,CAAC,CAAC;AAAA,EACnD,IAAI,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACzB,iBAAiB,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACtC,kBAAkB,EAAE,SAAS,EAAE,OAAO,CAAC;AACzC,CAAC;", "names": []}