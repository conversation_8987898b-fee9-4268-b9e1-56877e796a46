{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/startPush.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { componentDefinitionPath, componentPath } from \"./paths.js\";\nimport { nodeDependency, sourcePackage } from \"./modules.js\";\nimport { checkedComponent } from \"./checkedComponent.js\";\nimport { evaluatedComponentDefinition } from \"./componentDefinition.js\";\nimport {\n  appDefinitionConfig,\n  componentDefinitionConfig,\n} from \"./definitionConfig.js\";\nimport { authInfo } from \"./types.js\";\nimport { looseObject } from \"./utils.js\";\n\nexport const startPushRequest = looseObject({\n  adminKey: z.string(),\n  dryRun: z.boolean(),\n\n  functions: z.string(),\n\n  appDefinition: appDefinitionConfig,\n  componentDefinitions: z.array(componentDefinitionConfig),\n\n  nodeDependencies: z.array(nodeDependency),\n});\nexport type StartPushRequest = z.infer<typeof startPushRequest>;\n\nexport const schemaChange = looseObject({\n  allocatedComponentIds: z.any(),\n  schemaIds: z.any(),\n});\nexport type SchemaChange = z.infer<typeof schemaChange>;\n\nexport const startPushResponse = looseObject({\n  environmentVariables: z.record(z.string(), z.string()),\n\n  externalDepsId: z.nullable(z.string()),\n  componentDefinitionPackages: z.record(componentDefinitionPath, sourcePackage),\n\n  appAuth: z.array(authInfo),\n  analysis: z.record(componentDefinitionPath, evaluatedComponentDefinition),\n\n  app: checkedComponent,\n\n  schemaChange,\n});\nexport type StartPushResponse = z.infer<typeof startPushResponse>;\n\nexport const componentSchemaStatus = looseObject({\n  schemaValidationComplete: z.boolean(),\n  indexesComplete: z.number(),\n  indexesTotal: z.number(),\n});\nexport type ComponentSchemaStatus = z.infer<typeof componentSchemaStatus>;\n\nexport const schemaStatus = z.union([\n  looseObject({\n    type: z.literal(\"inProgress\"),\n    components: z.record(componentPath, componentSchemaStatus),\n  }),\n  looseObject({\n    type: z.literal(\"failed\"),\n    error: z.string(),\n    componentPath,\n    tableName: z.nullable(z.string()),\n  }),\n  looseObject({\n    type: z.literal(\"raceDetected\"),\n  }),\n  looseObject({\n    type: z.literal(\"complete\"),\n  }),\n]);\nexport type SchemaStatus = z.infer<typeof schemaStatus>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,yBAAyB,qBAAqB;AACvD,SAAS,gBAAgB,qBAAqB;AAC9C,SAAS,wBAAwB;AACjC,SAAS,oCAAoC;AAC7C;AAAA,EACE;AAAA,EACA;AAAA,OACK;AACP,SAAS,gBAAgB;AACzB,SAAS,mBAAmB;AAErB,aAAM,mBAAmB,YAAY;AAAA,EAC1C,UAAU,EAAE,OAAO;AAAA,EACnB,QAAQ,EAAE,QAAQ;AAAA,EAElB,WAAW,EAAE,OAAO;AAAA,EAEpB,eAAe;AAAA,EACf,sBAAsB,EAAE,MAAM,yBAAyB;AAAA,EAEvD,kBAAkB,EAAE,MAAM,cAAc;AAC1C,CAAC;AAGM,aAAM,eAAe,YAAY;AAAA,EACtC,uBAAuB,EAAE,IAAI;AAAA,EAC7B,WAAW,EAAE,IAAI;AACnB,CAAC;AAGM,aAAM,oBAAoB,YAAY;AAAA,EAC3C,sBAAsB,EAAE,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO,CAAC;AAAA,EAErD,gBAAgB,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EACrC,6BAA6B,EAAE,OAAO,yBAAyB,aAAa;AAAA,EAE5E,SAAS,EAAE,MAAM,QAAQ;AAAA,EACzB,UAAU,EAAE,OAAO,yBAAyB,4BAA4B;AAAA,EAExE,KAAK;AAAA,EAEL;AACF,CAAC;AAGM,aAAM,wBAAwB,YAAY;AAAA,EAC/C,0BAA0B,EAAE,QAAQ;AAAA,EACpC,iBAAiB,EAAE,OAAO;AAAA,EAC1B,cAAc,EAAE,OAAO;AACzB,CAAC;AAGM,aAAM,eAAe,EAAE,MAAM;AAAA,EAClC,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,YAAY;AAAA,IAC5B,YAAY,EAAE,OAAO,eAAe,qBAAqB;AAAA,EAC3D,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,IACxB,OAAO,EAAE,OAAO;AAAA,IAChB;AAAA,IACA,WAAW,EAAE,SAAS,EAAE,OAAO,CAAC;AAAA,EAClC,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,cAAc;AAAA,EAChC,CAAC;AAAA,EACD,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,UAAU;AAAA,EAC5B,CAAC;AACH,CAAC;", "names": []}