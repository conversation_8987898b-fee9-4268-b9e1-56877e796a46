{"version": 3, "sources": ["../../../../../src/cli/lib/deployApi/componentDefinition.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { canonicalizedModulePath, componentDefinitionPath } from \"./paths.js\";\nimport { Identifier, Reference, identifier, reference } from \"./types.js\";\nimport { analyzedModule, udfConfig } from \"./modules.js\";\nimport { looseObject } from \"./utils.js\";\nimport { convexValidator } from \"./validator.js\";\n\nexport const componentArgumentValidator = looseObject({\n  type: z.literal(\"value\"),\n  // Validator serialized to JSON.\n  value: z.string(),\n});\n\nexport const componentDefinitionType = z.union([\n  looseObject({ type: z.literal(\"app\") }),\n  looseObject({\n    type: z.literal(\"childComponent\"),\n    name: identifier,\n    args: z.array(z.tuple([identifier, componentArgumentValidator])),\n  }),\n]);\n\nexport const componentArgument = looseObject({\n  type: z.literal(\"value\"),\n  // Value serialized to JSON.\n  value: z.string(),\n});\n\nexport const componentInstantiation = looseObject({\n  name: identifier,\n  path: componentDefinitionPath,\n  args: z.nullable(z.array(z.tuple([identifier, componentArgument]))),\n});\n\nexport type ComponentExports =\n  | { type: \"leaf\"; leaf: Reference }\n  | { type: \"branch\"; branch: [Identifier, ComponentExports][] };\n\nexport const componentExports: z.ZodType<ComponentExports> = z.lazy(() =>\n  z.union([\n    looseObject({\n      type: z.literal(\"leaf\"),\n      leaf: reference,\n    }),\n    looseObject({\n      type: z.literal(\"branch\"),\n      branch: z.array(z.tuple([identifier, componentExports])),\n    }),\n  ]),\n);\n\nexport const componentDefinitionMetadata = looseObject({\n  path: componentDefinitionPath,\n  definitionType: componentDefinitionType,\n  childComponents: z.array(componentInstantiation),\n  httpMounts: z.record(z.string(), reference),\n  exports: looseObject({\n    type: z.literal(\"branch\"),\n    branch: z.array(z.tuple([identifier, componentExports])),\n  }),\n});\n\nexport const indexSchema = looseObject({\n  indexDescriptor: z.string(),\n  fields: z.array(z.string()),\n});\n\nexport const vectorIndexSchema = looseObject({\n  indexDescriptor: z.string(),\n  vectorField: z.string(),\n  dimensions: z.number().optional(),\n  filterFields: z.array(z.string()),\n});\n\nexport const searchIndexSchema = looseObject({\n  indexDescriptor: z.string(),\n  searchField: z.string(),\n  filterFields: z.array(z.string()),\n});\n\nexport const tableDefinition = looseObject({\n  tableName: z.string(),\n  indexes: z.array(indexSchema),\n  searchIndexes: z.array(searchIndexSchema).optional().nullable(),\n  vectorIndexes: z.array(vectorIndexSchema).optional().nullable(),\n  documentType: convexValidator,\n});\nexport type TableDefinition = z.infer<typeof tableDefinition>;\n\nexport const analyzedSchema = looseObject({\n  tables: z.array(tableDefinition),\n  schemaValidation: z.boolean(),\n});\nexport type AnalyzedSchema = z.infer<typeof analyzedSchema>;\n\nexport const evaluatedComponentDefinition = looseObject({\n  definition: componentDefinitionMetadata,\n  schema: analyzedSchema.optional().nullable(),\n  functions: z.record(canonicalizedModulePath, analyzedModule),\n  udfConfig,\n});\nexport type EvaluatedComponentDefinition = z.infer<\n  typeof evaluatedComponentDefinition\n>;\n"], "mappings": ";AAAA,SAAS,SAAS;AAClB,SAAS,yBAAyB,+BAA+B;AACjE,SAAgC,YAAY,iBAAiB;AAC7D,SAAS,gBAAgB,iBAAiB;AAC1C,SAAS,mBAAmB;AAC5B,SAAS,uBAAuB;AAEzB,aAAM,6BAA6B,YAAY;AAAA,EACpD,MAAM,EAAE,QAAQ,OAAO;AAAA;AAAA,EAEvB,OAAO,EAAE,OAAO;AAClB,CAAC;AAEM,aAAM,0BAA0B,EAAE,MAAM;AAAA,EAC7C,YAAY,EAAE,MAAM,EAAE,QAAQ,KAAK,EAAE,CAAC;AAAA,EACtC,YAAY;AAAA,IACV,MAAM,EAAE,QAAQ,gBAAgB;AAAA,IAChC,MAAM;AAAA,IACN,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,YAAY,0BAA0B,CAAC,CAAC;AAAA,EACjE,CAAC;AACH,CAAC;AAEM,aAAM,oBAAoB,YAAY;AAAA,EAC3C,MAAM,EAAE,QAAQ,OAAO;AAAA;AAAA,EAEvB,OAAO,EAAE,OAAO;AAClB,CAAC;AAEM,aAAM,yBAAyB,YAAY;AAAA,EAChD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,YAAY,iBAAiB,CAAC,CAAC,CAAC;AACpE,CAAC;AAMM,aAAM,mBAAgD,EAAE;AAAA,EAAK,MAClE,EAAE,MAAM;AAAA,IACN,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,MAAM;AAAA,MACtB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY;AAAA,MACV,MAAM,EAAE,QAAQ,QAAQ;AAAA,MACxB,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,YAAY,gBAAgB,CAAC,CAAC;AAAA,IACzD,CAAC;AAAA,EACH,CAAC;AACH;AAEO,aAAM,8BAA8B,YAAY;AAAA,EACrD,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,iBAAiB,EAAE,MAAM,sBAAsB;AAAA,EAC/C,YAAY,EAAE,OAAO,EAAE,OAAO,GAAG,SAAS;AAAA,EAC1C,SAAS,YAAY;AAAA,IACnB,MAAM,EAAE,QAAQ,QAAQ;AAAA,IACxB,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,YAAY,gBAAgB,CAAC,CAAC;AAAA,EACzD,CAAC;AACH,CAAC;AAEM,aAAM,cAAc,YAAY;AAAA,EACrC,iBAAiB,EAAE,OAAO;AAAA,EAC1B,QAAQ,EAAE,MAAM,EAAE,OAAO,CAAC;AAC5B,CAAC;AAEM,aAAM,oBAAoB,YAAY;AAAA,EAC3C,iBAAiB,EAAE,OAAO;AAAA,EAC1B,aAAa,EAAE,OAAO;AAAA,EACtB,YAAY,EAAE,OAAO,EAAE,SAAS;AAAA,EAChC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;AAClC,CAAC;AAEM,aAAM,oBAAoB,YAAY;AAAA,EAC3C,iBAAiB,EAAE,OAAO;AAAA,EAC1B,aAAa,EAAE,OAAO;AAAA,EACtB,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC;AAClC,CAAC;AAEM,aAAM,kBAAkB,YAAY;AAAA,EACzC,WAAW,EAAE,OAAO;AAAA,EACpB,SAAS,EAAE,MAAM,WAAW;AAAA,EAC5B,eAAe,EAAE,MAAM,iBAAiB,EAAE,SAAS,EAAE,SAAS;AAAA,EAC9D,eAAe,EAAE,MAAM,iBAAiB,EAAE,SAAS,EAAE,SAAS;AAAA,EAC9D,cAAc;AAChB,CAAC;AAGM,aAAM,iBAAiB,YAAY;AAAA,EACxC,QAAQ,EAAE,MAAM,eAAe;AAAA,EAC/B,kBAAkB,EAAE,QAAQ;AAC9B,CAAC;AAGM,aAAM,+BAA+B,YAAY;AAAA,EACtD,YAAY;AAAA,EACZ,QAAQ,eAAe,SAAS,EAAE,SAAS;AAAA,EAC3C,WAAW,EAAE,OAAO,yBAAyB,cAAc;AAAA,EAC3D;AACF,CAAC;", "names": []}