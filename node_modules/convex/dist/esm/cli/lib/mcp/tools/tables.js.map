{"version": 3, "sources": ["../../../../../../src/cli/lib/mcp/tools/tables.ts"], "sourcesContent": ["import { z } from \"zod\";\nimport { ConvexTool } from \"./index.js\";\nimport { loadSelectedDeploymentCredentials } from \"../../api.js\";\nimport { runSystemQuery } from \"../../run.js\";\nimport { deploymentFetch } from \"../../utils/utils.js\";\nimport { getDeploymentSelection } from \"../../deploymentSelection.js\";\n\nconst inputSchema = z.object({\n  deploymentSelector: z\n    .string()\n    .describe(\n      \"Deployment selector (from the status tool) to read tables from.\",\n    ),\n});\n\nconst outputSchema = z.object({\n  tables: z.record(\n    z.string(),\n    z.object({\n      schema: z.any().optional(),\n      inferredSchema: z.any().optional(),\n    }),\n  ),\n});\n\nexport const TablesTool: ConvexTool<typeof inputSchema, typeof outputSchema> = {\n  name: \"tables\",\n  description:\n    \"List all tables in a particular Convex deployment and their inferred and declared schema.\",\n  inputSchema,\n  outputSchema,\n  handler: async (ctx, args) => {\n    const { projectDir, deployment } = await ctx.decodeDeploymentSelector(\n      args.deploymentSelector,\n    );\n    process.chdir(projectDir);\n    const deploymentSelection = await getDeploymentSelection(ctx, ctx.options);\n    const credentials = await loadSelectedDeploymentCredentials(\n      ctx,\n      deploymentSelection,\n      deployment,\n    );\n    const schemaResponse: any = await runSystemQuery(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n      functionName: \"_system/frontend/getSchemas\",\n      componentPath: undefined,\n      args: {},\n    });\n    const schema: Record<string, z.infer<typeof activeSchemaEntry>> = {};\n    if (schemaResponse.active) {\n      const parsed = activeSchema.parse(JSON.parse(schemaResponse.active));\n      for (const table of parsed.tables) {\n        schema[table.tableName] = table;\n      }\n    }\n    const fetch = deploymentFetch(ctx, {\n      deploymentUrl: credentials.url,\n      adminKey: credentials.adminKey,\n    });\n    const response = await fetch(\"/api/shapes2\", {});\n    const shapesResult: Record<string, any> = await response.json();\n\n    const allTablesSet = new Set([\n      ...Object.keys(shapesResult),\n      ...Object.keys(schema),\n    ]);\n    const allTables = Array.from(allTablesSet);\n    allTables.sort();\n\n    const result: z.infer<typeof outputSchema>[\"tables\"] = {};\n    for (const table of allTables) {\n      result[table] = {\n        schema: schema[table],\n        inferredSchema: shapesResult[table],\n      };\n    }\n    return { tables: result };\n  },\n};\n\nconst activeSchemaEntry = z.object({\n  tableName: z.string(),\n  indexes: z.array(z.any()),\n  searchIndexes: z.array(z.any()),\n  vectorIndexes: z.array(z.any()),\n  documentType: z.any(),\n});\n\nconst activeSchema = z.object({ tables: z.array(activeSchemaEntry) });\n"], "mappings": ";AAAA,SAAS,SAAS;AAElB,SAAS,yCAAyC;AAClD,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,8BAA8B;AAEvC,MAAM,cAAc,EAAE,OAAO;AAAA,EAC3B,oBAAoB,EACjB,OAAO,EACP;AAAA,IACC;AAAA,EACF;AACJ,CAAC;AAED,MAAM,eAAe,EAAE,OAAO;AAAA,EAC5B,QAAQ,EAAE;AAAA,IACR,EAAE,OAAO;AAAA,IACT,EAAE,OAAO;AAAA,MACP,QAAQ,EAAE,IAAI,EAAE,SAAS;AAAA,MACzB,gBAAgB,EAAE,IAAI,EAAE,SAAS;AAAA,IACnC,CAAC;AAAA,EACH;AACF,CAAC;AAEM,aAAM,aAAkE;AAAA,EAC7E,MAAM;AAAA,EACN,aACE;AAAA,EACF;AAAA,EACA;AAAA,EACA,SAAS,OAAO,KAAK,SAAS;AAC5B,UAAM,EAAE,YAAY,WAAW,IAAI,MAAM,IAAI;AAAA,MAC3C,KAAK;AAAA,IACP;AACA,YAAQ,MAAM,UAAU;AACxB,UAAM,sBAAsB,MAAM,uBAAuB,KAAK,IAAI,OAAO;AACzE,UAAM,cAAc,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,UAAM,iBAAsB,MAAM,eAAe,KAAK;AAAA,MACpD,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,MACtB,cAAc;AAAA,MACd,eAAe;AAAA,MACf,MAAM,CAAC;AAAA,IACT,CAAC;AACD,UAAM,SAA4D,CAAC;AACnE,QAAI,eAAe,QAAQ;AACzB,YAAM,SAAS,aAAa,MAAM,KAAK,MAAM,eAAe,MAAM,CAAC;AACnE,iBAAW,SAAS,OAAO,QAAQ;AACjC,eAAO,MAAM,SAAS,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,UAAM,QAAQ,gBAAgB,KAAK;AAAA,MACjC,eAAe,YAAY;AAAA,MAC3B,UAAU,YAAY;AAAA,IACxB,CAAC;AACD,UAAM,WAAW,MAAM,MAAM,gBAAgB,CAAC,CAAC;AAC/C,UAAM,eAAoC,MAAM,SAAS,KAAK;AAE9D,UAAM,eAAe,oBAAI,IAAI;AAAA,MAC3B,GAAG,OAAO,KAAK,YAAY;AAAA,MAC3B,GAAG,OAAO,KAAK,MAAM;AAAA,IACvB,CAAC;AACD,UAAM,YAAY,MAAM,KAAK,YAAY;AACzC,cAAU,KAAK;AAEf,UAAM,SAAiD,CAAC;AACxD,eAAW,SAAS,WAAW;AAC7B,aAAO,KAAK,IAAI;AAAA,QACd,QAAQ,OAAO,KAAK;AAAA,QACpB,gBAAgB,aAAa,KAAK;AAAA,MACpC;AAAA,IACF;AACA,WAAO,EAAE,QAAQ,OAAO;AAAA,EAC1B;AACF;AAEA,MAAM,oBAAoB,EAAE,OAAO;AAAA,EACjC,WAAW,EAAE,OAAO;AAAA,EACpB,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC;AAAA,EACxB,eAAe,EAAE,MAAM,EAAE,IAAI,CAAC;AAAA,EAC9B,eAAe,EAAE,MAAM,EAAE,IAAI,CAAC;AAAA,EAC9B,cAAc,EAAE,IAAI;AACtB,CAAC;AAED,MAAM,eAAe,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,iBAAiB,EAAE,CAAC;", "names": []}