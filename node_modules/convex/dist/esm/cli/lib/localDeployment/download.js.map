{"version": 3, "sources": ["../../../../../src/cli/lib/localDeployment/download.ts"], "sourcesContent": ["import AdmZip from \"adm-zip\";\nimport {\n  Context,\n  logFinishedStep,\n  startLogProgress,\n  logVerbose,\n  logMessage,\n} from \"../../../bundler/context.js\";\nimport {\n  dashboardZip,\n  executablePath,\n  versionedBinaryDir,\n  dashboardOutDir,\n  resetDashboardDir,\n  loadDashboardConfig,\n  executableName,\n} from \"./filePaths.js\";\nimport child_process from \"child_process\";\nimport { promisify } from \"util\";\nimport { Readable } from \"stream\";\nimport { TempPath, nodeFs, withTmpDir } from \"../../../bundler/fs.js\";\nimport { components } from \"@octokit/openapi-types\";\nimport { recursivelyDelete, recursivelyCopy } from \"../fsUtils.js\";\nimport { LocalDeploymentError } from \"./errors.js\";\nimport ProgressBar from \"progress\";\nimport path from \"path\";\n\nasync function makeExecutable(p: string) {\n  switch (process.platform) {\n    case \"darwin\":\n    case \"linux\": {\n      await promisify(child_process.exec)(`chmod +x ${p}`);\n    }\n  }\n}\n\ntype GitHubRelease = components[\"schemas\"][\"release\"];\n\nexport async function ensureBackendBinaryDownloaded(\n  ctx: Context,\n  version: { kind: \"latest\" } | { kind: \"version\"; version: string },\n): Promise<{ binaryPath: string; version: string }> {\n  if (version.kind === \"version\") {\n    return _ensureBackendBinaryDownloaded(ctx, version.version);\n  }\n  const latestVersionWithBinary = await findLatestVersionWithBinary(ctx);\n  return _ensureBackendBinaryDownloaded(ctx, latestVersionWithBinary);\n}\n\nasync function _ensureBackendBinaryDownloaded(\n  ctx: Context,\n  version: string,\n): Promise<{ binaryPath: string; version: string }> {\n  logVerbose(ctx, `Ensuring backend binary downloaded for version ${version}`);\n  const existingDownload = await checkForExistingDownload(ctx, version);\n  if (existingDownload !== null) {\n    logVerbose(ctx, `Using existing download at ${existingDownload}`);\n    return {\n      binaryPath: existingDownload,\n      version,\n    };\n  }\n  const binaryPath = await downloadBackendBinary(ctx, version);\n  return { version, binaryPath };\n}\n\n/**\n * Parse the HTTP header like\n * link: <https://api.github.com/repositories/1300192/issues?page=2>; rel=\"prev\", <https://api.github.com/repositories/1300192/issues?page=4>; rel=\"next\", <https://api.github.com/repositories/1300192/issues?page=515>; rel=\"last\", <https://api.github.com/repositories/1300192/issues?page=1>; rel=\"first\"\n * into an object.\n * https://docs.github.com/en/rest/using-the-rest-api/using-pagination-in-the-rest-api?apiVersion=2022-11-28#using-link-headers\n */\nfunction parseLinkHeader(header: string): {\n  prev?: string;\n  next?: string;\n  first?: string;\n  last?: string;\n} {\n  const links: { [key: string]: string } = {};\n  const parts = header.split(\",\");\n  for (const part of parts) {\n    const section = part.split(\";\");\n    if (section.length !== 2) {\n      continue;\n    }\n    const url = section[0].trim().slice(1, -1);\n    const rel = section[1].trim().slice(5, -1);\n    links[rel] = url;\n  }\n  return links;\n}\n\n/**\n * Finds the latest version of the convex backend that has a binary that works\n * on this platform.\n */\nexport async function findLatestVersionWithBinary(\n  ctx: Context,\n): Promise<string> {\n  const targetName = getDownloadPath();\n  logVerbose(\n    ctx,\n    `Finding latest stable release containing binary named ${targetName}`,\n  );\n  let latestVersion: string | undefined;\n  let nextUrl =\n    \"https://api.github.com/repos/get-convex/convex-backend/releases?per_page=30\";\n\n  try {\n    while (nextUrl) {\n      const response = await fetch(nextUrl);\n\n      if (!response.ok) {\n        const text = await response.text();\n        return await ctx.crash({\n          exitCode: 1,\n          errorType: \"fatal\",\n          printedMessage: `GitHub API returned ${response.status}: ${text}`,\n          errForSentry: new LocalDeploymentError(\n            `GitHub API returned ${response.status}: ${text}`,\n          ),\n        });\n      }\n\n      const releases = (await response.json()) as GitHubRelease[];\n      if (releases.length === 0) {\n        break;\n      }\n\n      for (const release of releases) {\n        // Track the latest stable version we've seen even if it doesn't have our binary\n        if (!latestVersion && !release.prerelease && !release.draft) {\n          latestVersion = release.tag_name;\n          logVerbose(ctx, `Latest stable version is ${latestVersion}`);\n        }\n\n        // Only consider stable releases\n        if (!release.prerelease && !release.draft) {\n          // Check if this release has our binary\n          if (release.assets.find((asset) => asset.name === targetName)) {\n            logVerbose(\n              ctx,\n              `Latest stable version with appropriate binary is ${release.tag_name}`,\n            );\n            return release.tag_name;\n          }\n\n          logVerbose(\n            ctx,\n            `Version ${release.tag_name} does not contain a ${targetName}, checking previous version`,\n          );\n        }\n      }\n\n      // Get the next page URL from the Link header\n      const linkHeader = response.headers.get(\"Link\");\n      if (!linkHeader) {\n        break;\n      }\n\n      const links = parseLinkHeader(linkHeader);\n      nextUrl = links[\"next\"] || \"\";\n    }\n\n    // If we get here, we didn't find any suitable releases\n    if (!latestVersion) {\n      return await ctx.crash({\n        exitCode: 1,\n        errorType: \"fatal\",\n        printedMessage:\n          \"Found no non-draft, non-prerelease convex backend releases.\",\n        errForSentry: new LocalDeploymentError(\n          \"Found no non-draft, non-prerelease convex backend releases.\",\n        ),\n      });\n    }\n\n    // If we found stable releases but none had our binary\n    const message = `Failed to find a convex backend release that contained ${targetName}.`;\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: message,\n      errForSentry: new LocalDeploymentError(message),\n    });\n  } catch (e) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: \"Failed to get latest convex backend releases\",\n      errForSentry: new LocalDeploymentError(e?.toString()),\n    });\n  }\n}\n\n/**\n *\n * @param ctx\n * @param version\n * @returns The binary path if it exists, or null\n */\nasync function checkForExistingDownload(\n  ctx: Context,\n  version: string,\n): Promise<string | null> {\n  const destDir = versionedBinaryDir(version);\n  if (!ctx.fs.exists(destDir)) {\n    return null;\n  }\n  const p = executablePath(version);\n  if (!ctx.fs.exists(p)) {\n    // This directory isn't what we expected. Remove it.\n    recursivelyDelete(ctx, destDir, { force: true });\n    return null;\n  }\n  await makeExecutable(p);\n  return p;\n}\n\nasync function downloadBackendBinary(\n  ctx: Context,\n  version: string,\n): Promise<string> {\n  const downloadPath = getDownloadPath();\n  // Note: We validate earlier that there's a binary for this platform at the specified version,\n  // so in practice, we should never hit errors here.\n  if (downloadPath === null) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `Unsupported platform ${process.platform} and architecture ${process.arch} for local deployment.`,\n    });\n  }\n  await downloadZipFile(ctx, {\n    version,\n    filename: downloadPath,\n    nameForLogging: \"Convex backend binary\",\n    onDownloadComplete: async (ctx, unzippedPath) => {\n      const name = executableName();\n      const tempExecPath = path.join(unzippedPath, name);\n      await makeExecutable(tempExecPath);\n      logVerbose(ctx, \"Marked as executable\");\n      ctx.fs.mkdir(versionedBinaryDir(version), { recursive: true });\n      ctx.fs.swapTmpFile(tempExecPath as TempPath, executablePath(version));\n    },\n  });\n  return executablePath(version);\n}\n\n/**\n * Get the artifact name, composed of the target convex-local-backend and\n * the Rust \"target triple\" appropriate for the current machine.\n **/\nfunction getDownloadPath() {\n  switch (process.platform) {\n    case \"darwin\":\n      if (process.arch === \"arm64\") {\n        return \"convex-local-backend-aarch64-apple-darwin.zip\";\n      } else if (process.arch === \"x64\") {\n        return \"convex-local-backend-x86_64-apple-darwin.zip\";\n      }\n      break;\n    case \"linux\":\n      if (process.arch === \"arm64\") {\n        return \"convex-local-backend-aarch64-unknown-linux-gnu.zip\";\n      } else if (process.arch === \"x64\") {\n        return \"convex-local-backend-x86_64-unknown-linux-gnu.zip\";\n      }\n      break;\n    case \"win32\":\n      return \"convex-local-backend-x86_64-pc-windows-msvc.zip\";\n  }\n  return null;\n}\n\nfunction getGithubDownloadUrl(version: string, filename: string) {\n  return `https://github.com/get-convex/convex-backend/releases/download/${version}/${filename}`;\n}\n\nasync function downloadZipFile(\n  ctx: Context,\n  args: {\n    version: string;\n    filename: string;\n    nameForLogging: string;\n    onDownloadComplete: (ctx: Context, unzippedPath: TempPath) => Promise<void>;\n  },\n) {\n  const { version, filename, nameForLogging } = args;\n  const url = getGithubDownloadUrl(version, filename);\n  const response = await fetch(url);\n  const contentLength = parseInt(\n    response.headers.get(\"content-length\") ?? \"\",\n    10,\n  );\n  let progressBar: ProgressBar | null = null;\n  if (!isNaN(contentLength) && contentLength !== 0 && process.stdout.isTTY) {\n    progressBar = startLogProgress(\n      ctx,\n      `Downloading ${nameForLogging} [:bar] :percent :etas`,\n      {\n        width: 40,\n        total: contentLength,\n        clear: true,\n      },\n    );\n  } else {\n    logMessage(ctx, `Downloading ${nameForLogging}`);\n  }\n  if (response.status !== 200) {\n    return await ctx.crash({\n      exitCode: 1,\n      errorType: \"fatal\",\n      printedMessage: `File not found at ${url}.`,\n    });\n  }\n  await withTmpDir(async (tmpDir) => {\n    logVerbose(ctx, `Created tmp dir ${tmpDir.path}`);\n    // Create a file in the tmp dir\n    const zipLocation = tmpDir.registerTempPath(null);\n    const readable = Readable.fromWeb(response.body! as any);\n    await tmpDir.writeFileStream(zipLocation, readable, (chunk: any) => {\n      if (progressBar !== null) {\n        progressBar.tick(chunk.length);\n      }\n    });\n    if (progressBar) {\n      progressBar.terminate();\n      logFinishedStep(ctx, `Downloaded ${nameForLogging}`);\n    }\n    logVerbose(ctx, \"Downloaded zip file\");\n\n    const zip = new AdmZip(zipLocation);\n    await withTmpDir(async (versionDir) => {\n      logVerbose(ctx, `Created tmp dir ${versionDir.path}`);\n      zip.extractAllTo(versionDir.path, true);\n      logVerbose(ctx, \"Extracted from zip file\");\n      await args.onDownloadComplete(ctx, versionDir.path);\n    });\n  });\n  return executablePath(version);\n}\n\nexport async function ensureDashboardDownloaded(ctx: Context, version: string) {\n  const config = loadDashboardConfig(ctx);\n  if (config !== null && config.version === version) {\n    return;\n  }\n  await resetDashboardDir(ctx);\n  await _ensureDashboardDownloaded(ctx, version);\n}\nasync function _ensureDashboardDownloaded(ctx: Context, version: string) {\n  const zipLocation = dashboardZip();\n  if (ctx.fs.exists(zipLocation)) {\n    ctx.fs.unlink(zipLocation);\n  }\n  const outDir = dashboardOutDir();\n  await downloadZipFile(ctx, {\n    version,\n    filename: \"dashboard.zip\",\n    nameForLogging: \"Convex dashboard\",\n    onDownloadComplete: async (ctx, unzippedPath) => {\n      await recursivelyCopy(ctx, nodeFs, unzippedPath, outDir);\n      logVerbose(ctx, \"Copied into out dir\");\n    },\n  });\n  return outDir;\n}\n"], "mappings": ";AAAA,OAAO,YAAY;AACnB;AAAA,EAEE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP;AAAA,EACE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACK;AACP,OAAO,mBAAmB;AAC1B,SAAS,iBAAiB;AAC1B,SAAS,gBAAgB;AACzB,SAAmB,QAAQ,kBAAkB;AAE7C,SAAS,mBAAmB,uBAAuB;AACnD,SAAS,4BAA4B;AAErC,OAAO,UAAU;AAEjB,eAAe,eAAe,GAAW;AACvC,UAAQ,QAAQ,UAAU;AAAA,IACxB,KAAK;AAAA,IACL,KAAK,SAAS;AACZ,YAAM,UAAU,cAAc,IAAI,EAAE,YAAY,CAAC,EAAE;AAAA,IACrD;AAAA,EACF;AACF;AAIA,sBAAsB,8BACpB,KACA,SACkD;AAClD,MAAI,QAAQ,SAAS,WAAW;AAC9B,WAAO,+BAA+B,KAAK,QAAQ,OAAO;AAAA,EAC5D;AACA,QAAM,0BAA0B,MAAM,4BAA4B,GAAG;AACrE,SAAO,+BAA+B,KAAK,uBAAuB;AACpE;AAEA,eAAe,+BACb,KACA,SACkD;AAClD,aAAW,KAAK,kDAAkD,OAAO,EAAE;AAC3E,QAAM,mBAAmB,MAAM,yBAAyB,KAAK,OAAO;AACpE,MAAI,qBAAqB,MAAM;AAC7B,eAAW,KAAK,8BAA8B,gBAAgB,EAAE;AAChE,WAAO;AAAA,MACL,YAAY;AAAA,MACZ;AAAA,IACF;AAAA,EACF;AACA,QAAM,aAAa,MAAM,sBAAsB,KAAK,OAAO;AAC3D,SAAO,EAAE,SAAS,WAAW;AAC/B;AAQA,SAAS,gBAAgB,QAKvB;AACA,QAAM,QAAmC,CAAC;AAC1C,QAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,aAAW,QAAQ,OAAO;AACxB,UAAM,UAAU,KAAK,MAAM,GAAG;AAC9B,QAAI,QAAQ,WAAW,GAAG;AACxB;AAAA,IACF;AACA,UAAM,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,EAAE;AACzC,UAAM,MAAM,QAAQ,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,EAAE;AACzC,UAAM,GAAG,IAAI;AAAA,EACf;AACA,SAAO;AACT;AAMA,sBAAsB,4BACpB,KACiB;AACjB,QAAM,aAAa,gBAAgB;AACnC;AAAA,IACE;AAAA,IACA,yDAAyD,UAAU;AAAA,EACrE;AACA,MAAI;AACJ,MAAI,UACF;AAEF,MAAI;AACF,WAAO,SAAS;AACd,YAAM,WAAW,MAAM,MAAM,OAAO;AAEpC,UAAI,CAAC,SAAS,IAAI;AAChB,cAAM,OAAO,MAAM,SAAS,KAAK;AACjC,eAAO,MAAM,IAAI,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,WAAW;AAAA,UACX,gBAAgB,uBAAuB,SAAS,MAAM,KAAK,IAAI;AAAA,UAC/D,cAAc,IAAI;AAAA,YAChB,uBAAuB,SAAS,MAAM,KAAK,IAAI;AAAA,UACjD;AAAA,QACF,CAAC;AAAA,MACH;AAEA,YAAM,WAAY,MAAM,SAAS,KAAK;AACtC,UAAI,SAAS,WAAW,GAAG;AACzB;AAAA,MACF;AAEA,iBAAW,WAAW,UAAU;AAE9B,YAAI,CAAC,iBAAiB,CAAC,QAAQ,cAAc,CAAC,QAAQ,OAAO;AAC3D,0BAAgB,QAAQ;AACxB,qBAAW,KAAK,4BAA4B,aAAa,EAAE;AAAA,QAC7D;AAGA,YAAI,CAAC,QAAQ,cAAc,CAAC,QAAQ,OAAO;AAEzC,cAAI,QAAQ,OAAO,KAAK,CAAC,UAAU,MAAM,SAAS,UAAU,GAAG;AAC7D;AAAA,cACE;AAAA,cACA,oDAAoD,QAAQ,QAAQ;AAAA,YACtE;AACA,mBAAO,QAAQ;AAAA,UACjB;AAEA;AAAA,YACE;AAAA,YACA,WAAW,QAAQ,QAAQ,uBAAuB,UAAU;AAAA,UAC9D;AAAA,QACF;AAAA,MACF;AAGA,YAAM,aAAa,SAAS,QAAQ,IAAI,MAAM;AAC9C,UAAI,CAAC,YAAY;AACf;AAAA,MACF;AAEA,YAAM,QAAQ,gBAAgB,UAAU;AACxC,gBAAU,MAAM,MAAM,KAAK;AAAA,IAC7B;AAGA,QAAI,CAAC,eAAe;AAClB,aAAO,MAAM,IAAI,MAAM;AAAA,QACrB,UAAU;AAAA,QACV,WAAW;AAAA,QACX,gBACE;AAAA,QACF,cAAc,IAAI;AAAA,UAChB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAGA,UAAM,UAAU,0DAA0D,UAAU;AACpF,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc,IAAI,qBAAqB,OAAO;AAAA,IAChD,CAAC;AAAA,EACH,SAAS,GAAG;AACV,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB;AAAA,MAChB,cAAc,IAAI,qBAAqB,GAAG,SAAS,CAAC;AAAA,IACtD,CAAC;AAAA,EACH;AACF;AAQA,eAAe,yBACb,KACA,SACwB;AACxB,QAAM,UAAU,mBAAmB,OAAO;AAC1C,MAAI,CAAC,IAAI,GAAG,OAAO,OAAO,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,QAAM,IAAI,eAAe,OAAO;AAChC,MAAI,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG;AAErB,sBAAkB,KAAK,SAAS,EAAE,OAAO,KAAK,CAAC;AAC/C,WAAO;AAAA,EACT;AACA,QAAM,eAAe,CAAC;AACtB,SAAO;AACT;AAEA,eAAe,sBACb,KACA,SACiB;AACjB,QAAM,eAAe,gBAAgB;AAGrC,MAAI,iBAAiB,MAAM;AACzB,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,wBAAwB,QAAQ,QAAQ,qBAAqB,QAAQ,IAAI;AAAA,IAC3F,CAAC;AAAA,EACH;AACA,QAAM,gBAAgB,KAAK;AAAA,IACzB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,oBAAoB,OAAOA,MAAK,iBAAiB;AAC/C,YAAM,OAAO,eAAe;AAC5B,YAAM,eAAe,KAAK,KAAK,cAAc,IAAI;AACjD,YAAM,eAAe,YAAY;AACjC,iBAAWA,MAAK,sBAAsB;AACtC,MAAAA,KAAI,GAAG,MAAM,mBAAmB,OAAO,GAAG,EAAE,WAAW,KAAK,CAAC;AAC7D,MAAAA,KAAI,GAAG,YAAY,cAA0B,eAAe,OAAO,CAAC;AAAA,IACtE;AAAA,EACF,CAAC;AACD,SAAO,eAAe,OAAO;AAC/B;AAMA,SAAS,kBAAkB;AACzB,UAAQ,QAAQ,UAAU;AAAA,IACxB,KAAK;AACH,UAAI,QAAQ,SAAS,SAAS;AAC5B,eAAO;AAAA,MACT,WAAW,QAAQ,SAAS,OAAO;AACjC,eAAO;AAAA,MACT;AACA;AAAA,IACF,KAAK;AACH,UAAI,QAAQ,SAAS,SAAS;AAC5B,eAAO;AAAA,MACT,WAAW,QAAQ,SAAS,OAAO;AACjC,eAAO;AAAA,MACT;AACA;AAAA,IACF,KAAK;AACH,aAAO;AAAA,EACX;AACA,SAAO;AACT;AAEA,SAAS,qBAAqB,SAAiB,UAAkB;AAC/D,SAAO,kEAAkE,OAAO,IAAI,QAAQ;AAC9F;AAEA,eAAe,gBACb,KACA,MAMA;AACA,QAAM,EAAE,SAAS,UAAU,eAAe,IAAI;AAC9C,QAAM,MAAM,qBAAqB,SAAS,QAAQ;AAClD,QAAM,WAAW,MAAM,MAAM,GAAG;AAChC,QAAM,gBAAgB;AAAA,IACpB,SAAS,QAAQ,IAAI,gBAAgB,KAAK;AAAA,IAC1C;AAAA,EACF;AACA,MAAI,cAAkC;AACtC,MAAI,CAAC,MAAM,aAAa,KAAK,kBAAkB,KAAK,QAAQ,OAAO,OAAO;AACxE,kBAAc;AAAA,MACZ;AAAA,MACA,eAAe,cAAc;AAAA,MAC7B;AAAA,QACE,OAAO;AAAA,QACP,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,KAAK,eAAe,cAAc,EAAE;AAAA,EACjD;AACA,MAAI,SAAS,WAAW,KAAK;AAC3B,WAAO,MAAM,IAAI,MAAM;AAAA,MACrB,UAAU;AAAA,MACV,WAAW;AAAA,MACX,gBAAgB,qBAAqB,GAAG;AAAA,IAC1C,CAAC;AAAA,EACH;AACA,QAAM,WAAW,OAAO,WAAW;AACjC,eAAW,KAAK,mBAAmB,OAAO,IAAI,EAAE;AAEhD,UAAM,cAAc,OAAO,iBAAiB,IAAI;AAChD,UAAM,WAAW,SAAS,QAAQ,SAAS,IAAY;AACvD,UAAM,OAAO,gBAAgB,aAAa,UAAU,CAAC,UAAe;AAClE,UAAI,gBAAgB,MAAM;AACxB,oBAAY,KAAK,MAAM,MAAM;AAAA,MAC/B;AAAA,IACF,CAAC;AACD,QAAI,aAAa;AACf,kBAAY,UAAU;AACtB,sBAAgB,KAAK,cAAc,cAAc,EAAE;AAAA,IACrD;AACA,eAAW,KAAK,qBAAqB;AAErC,UAAM,MAAM,IAAI,OAAO,WAAW;AAClC,UAAM,WAAW,OAAO,eAAe;AACrC,iBAAW,KAAK,mBAAmB,WAAW,IAAI,EAAE;AACpD,UAAI,aAAa,WAAW,MAAM,IAAI;AACtC,iBAAW,KAAK,yBAAyB;AACzC,YAAM,KAAK,mBAAmB,KAAK,WAAW,IAAI;AAAA,IACpD,CAAC;AAAA,EACH,CAAC;AACD,SAAO,eAAe,OAAO;AAC/B;AAEA,sBAAsB,0BAA0B,KAAc,SAAiB;AAC7E,QAAM,SAAS,oBAAoB,GAAG;AACtC,MAAI,WAAW,QAAQ,OAAO,YAAY,SAAS;AACjD;AAAA,EACF;AACA,QAAM,kBAAkB,GAAG;AAC3B,QAAM,2BAA2B,KAAK,OAAO;AAC/C;AACA,eAAe,2BAA2B,KAAc,SAAiB;AACvE,QAAM,cAAc,aAAa;AACjC,MAAI,IAAI,GAAG,OAAO,WAAW,GAAG;AAC9B,QAAI,GAAG,OAAO,WAAW;AAAA,EAC3B;AACA,QAAM,SAAS,gBAAgB;AAC/B,QAAM,gBAAgB,KAAK;AAAA,IACzB;AAAA,IACA,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,oBAAoB,OAAOA,MAAK,iBAAiB;AAC/C,YAAM,gBAAgBA,MAAK,QAAQ,cAAc,MAAM;AACvD,iBAAWA,MAAK,qBAAqB;AAAA,IACvC;AAAA,EACF,CAAC;AACD,SAAO;AACT;", "names": ["ctx"]}