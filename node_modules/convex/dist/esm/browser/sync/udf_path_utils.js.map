{"version": 3, "sources": ["../../../../src/browser/sync/udf_path_utils.ts"], "sourcesContent": ["import { convexTo<PERSON>son, Value } from \"../../values/index.js\";\n\nexport function canonicalizeUdfPath(udfPath: string): string {\n  const pieces = udfPath.split(\":\");\n  let moduleName: string;\n  let functionName: string;\n  if (pieces.length === 1) {\n    moduleName = pieces[0];\n    functionName = \"default\";\n  } else {\n    moduleName = pieces.slice(0, pieces.length - 1).join(\":\");\n    functionName = pieces[pieces.length - 1];\n  }\n  if (moduleName.endsWith(\".js\")) {\n    moduleName = moduleName.slice(0, -3);\n  }\n  return `${moduleName}:${functionName}`;\n}\n\n/**\n * A string representing the name and arguments of a query.\n *\n * This is used by the {@link BaseConvexClient}.\n *\n * @public\n */\nexport type QueryToken = string;\n\nexport function serializePathAndArgs(\n  udfPath: string,\n  args: Record<string, Value>,\n): QueryToken {\n  return JSON.stringify({\n    udfPath: canonicalizeUdfPath(udfPath),\n    args: convexT<PERSON><PERSON><PERSON>(args),\n  });\n}\n"], "mappings": ";AAAA,SAAS,oBAA2B;AAE7B,gBAAS,oBAAoB,SAAyB;AAC3D,QAAM,SAAS,QAAQ,MAAM,GAAG;AAChC,MAAI;AACJ,MAAI;AACJ,MAAI,OAAO,WAAW,GAAG;AACvB,iBAAa,OAAO,CAAC;AACrB,mBAAe;AAAA,EACjB,OAAO;AACL,iBAAa,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC,EAAE,KAAK,GAAG;AACxD,mBAAe,OAAO,OAAO,SAAS,CAAC;AAAA,EACzC;AACA,MAAI,WAAW,SAAS,KAAK,GAAG;AAC9B,iBAAa,WAAW,MAAM,GAAG,EAAE;AAAA,EACrC;AACA,SAAO,GAAG,UAAU,IAAI,YAAY;AACtC;AAWO,gBAAS,qBACd,SACA,MACY;AACZ,SAAO,KAAK,UAAU;AAAA,IACpB,SAAS,oBAAoB,OAAO;AAAA,IACpC,MAAM,aAAa,IAAI;AAAA,EACzB,CAAC;AACH;", "names": []}