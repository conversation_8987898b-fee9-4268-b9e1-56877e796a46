{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///mnt/windows/vol1/ai_agent_code_review/app/ConvexClientProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ConvexProvider, ConvexReactClient } from \"convex/react\";\nimport { ReactNode } from \"react\";\n\nconst convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport function ConvexClientProvider({ children }: { children: ReactNode }) {\n  return <ConvexProvider client={convex}>{children}</ConvexProvider>;\n}"], "names": [], "mappings": ";;;AAKqC;;AAHrC;AAAA;AAFA;;;AAKA,MAAM,SAAS,IAAI,2JAAA,CAAA,oBAAiB;AAE7B,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;IACxE,qBAAO,6LAAC,2JAAA,CAAA,iBAAc;QAAC,QAAQ;kBAAS;;;;;;AAC1C;KAFgB", "debugId": null}}]}