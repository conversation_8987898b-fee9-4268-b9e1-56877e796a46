{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///mnt/windows/vol1/ai_agent_code_review/convex/_generated/api.js"], "sourcesContent": ["/* eslint-disable */\n/**\n * Generated `api` utility.\n *\n * THIS CODE IS AUTOMATICALLY GENERATED.\n *\n * To regenerate, run `npx convex dev`.\n * @module\n */\n\nimport { anyApi } from \"convex/server\";\n\n/**\n * A utility for referencing Convex functions in your app's API.\n *\n * Usage:\n * ```js\n * const myFunctionReference = api.myModule.myFunction;\n * ```\n */\nexport const api = anyApi;\nexport const internal = anyApi;\n"], "names": [], "mappings": "AAAA,kBAAkB,GAClB;;;;;;;CAOC;;;;AAED;AAAA;;AAUO,MAAM,MAAM,yJAAA,CAAA,SAAM;AAClB,MAAM,WAAW,yJAAA,CAAA,SAAM", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///mnt/windows/vol1/ai_agent_code_review/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useQuery } from \"convex/react\";\nimport { api } from \"../convex/_generated/api\";\n\nexport default function Home() {\n  const tasks = useQuery(api.tasks.get);\n  return (\n    <main className=\"flex min-h-screen flex-col items-center justify-between p-24\">\n      {tasks?.map(({ _id, text }) => <div key={_id}>{text}</div>)}\n    </main>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;AAHA;;;AAKe,SAAS;;IACtB,MAAM,QAAQ,CAAA,GAAA,2JAAA,CAAA,WAAQ,AAAD,EAAE,8HAAA,CAAA,MAAG,CAAC,KAAK,CAAC,GAAG;IACpC,qBACE,6LAAC;QAAK,WAAU;kBACb,OAAO,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,iBAAK,6LAAC;0BAAe;eAAN;;;;;;;;;;AAG/C;GAPwB;;QACR,2JAAA,CAAA,WAAQ;;;KADA", "debugId": null}}]}