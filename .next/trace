[{"name":"hot-reloader","duration":165,"timestamp":2407220695,"id":3,"tags":{"version":"15.3.4"},"startTime":1750306701208,"traceId":"707eda45df73d980"},{"name":"setup-dev-bundler","duration":3697306,"timestamp":2406690587,"id":2,"parentId":1,"tags":{},"startTime":1750306700678,"traceId":"707eda45df73d980"},{"name":"run-instrumentation-hook","duration":28,"timestamp":2411166126,"id":4,"parentId":1,"tags":{},"startTime":1750306705153,"traceId":"707eda45df73d980"},{"name":"start-dev-server","duration":8816579,"timestamp":2402359640,"id":1,"tags":{"cpus":"4","platform":"linux","memory.freeMem":"2209767424","memory.totalMem":"8085979136","memory.heapSizeLimit":"4092592128","memory.rss":"248504320","memory.heapTotal":"99028992","memory.heapUsed":"71501992"},"startTime":1750306696347,"traceId":"707eda45df73d980"},{"name":"compile-path","duration":13476862,"timestamp":2412603232,"id":7,"tags":{"trigger":"/"},"startTime":1750306706591,"traceId":"707eda45df73d980"},{"name":"ensure-page","duration":13477790,"timestamp":2412602738,"id":6,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750306706590,"traceId":"707eda45df73d980"}]
[{"name":"ensure-page","duration":36119,"timestamp":2426087378,"id":8,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750306720075,"traceId":"707eda45df73d980"},{"name":"handle-request","duration":15357108,"timestamp":2412596299,"id":5,"tags":{"url":"/"},"startTime":1750306706584,"traceId":"707eda45df73d980"},{"name":"memory-usage","duration":8,"timestamp":2427953580,"id":9,"parentId":5,"tags":{"url":"/","memory.rss":"527581184","memory.heapUsed":"86452144","memory.heapTotal":"101507072"},"startTime":1750306721941,"traceId":"707eda45df73d980"},{"name":"client-hmr-latency","duration":1045000,"timestamp":2478140557,"id":13,"parentId":3,"tags":{"updatedModules":["[project]/node_modules/convex/dist/esm/server/database.js","[project]/node_modules/convex/dist/esm/server/impl/syscall.js","[project]/node_modules/convex/dist/esm/server/impl/actions_impl.js","[project]/node_modules/convex/dist/esm/server/vector_search.js","[project]/node_modules/convex/dist/esm/server/impl/validate.js","[project]/node_modules/convex/dist/esm/server/impl/vector_search_impl.js","[project]/node_modules/convex/dist/esm/server/impl/authentication_impl.js","[project]/node_modules/convex/dist/esm/server/filter_builder.js","[project]/node_modules/convex/dist/esm/server/impl/filter_builder_impl.js","[project]/node_modules/convex/dist/esm/server/index_range_builder.js","[project]/node_modules/convex/dist/esm/server/impl/index_range_builder_impl.js","[project]/node_modules/convex/dist/esm/server/search_filter_builder.js","[project]/node_modules/convex/dist/esm/server/impl/search_filter_builder_impl.js","[project]/node_modules/convex/dist/esm/server/impl/query_impl.js","[project]/node_modules/convex/dist/esm/server/impl/database_impl.js","[project]/node_modules/convex/dist/esm/server/impl/scheduler_impl.js","[project]/node_modules/convex/dist/esm/server/impl/storage_impl.js","[project]/node_modules/convex/dist/esm/server/impl/registration_impl.js","[project]/node_modules/convex/dist/esm/server/pagination.js","[project]/node_modules/convex/dist/esm/server/storage.js","[project]/node_modules/convex/dist/esm/server/cron.js","[project]/node_modules/convex/dist/esm/server/router.js","[project]/node_modules/convex/dist/esm/server/components/index.js","[project]/node_modules/convex/dist/esm/server/schema.js","[project]/node_modules/convex/dist/esm/server/index.js","[project]/convex/_generated/api.js","[project]/app/page.tsx"],"page":"/","isPageHidden":false},"startTime":1750306773202,"traceId":"707eda45df73d980"},{"name":"compile-path","duration":328963,"timestamp":2479054239,"id":12,"tags":{"trigger":"/"},"startTime":1750306773042,"traceId":"707eda45df73d980"}]
[{"name":"ensure-page","duration":182669,"timestamp":2479398514,"id":14,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750306773386,"traceId":"707eda45df73d980"},{"name":"handle-request","duration":985386,"timestamp":2479039154,"id":10,"tags":{"url":"/?_rsc=1oqwi"},"startTime":1750306773027,"traceId":"707eda45df73d980"},{"name":"memory-usage","duration":13,"timestamp":2480024718,"id":15,"parentId":10,"tags":{"url":"/?_rsc=1oqwi","memory.rss":"544026624","memory.heapUsed":"95216672","memory.heapTotal":"106274816"},"startTime":1750306774012,"traceId":"707eda45df73d980"},{"name":"client-hmr-latency","duration":740000,"timestamp":2506503604,"id":18,"parentId":3,"tags":{"updatedModules":["[project]/app/page.tsx"],"page":"/","isPageHidden":false},"startTime":1750306801350,"traceId":"707eda45df73d980"},{"name":"ensure-page","duration":170304,"timestamp":2507303611,"id":17,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750306801291,"traceId":"707eda45df73d980"},{"name":"ensure-page","duration":68551,"timestamp":2507485052,"id":19,"parentId":3,"tags":{"inputPage":"/page"},"startTime":1750306801472,"traceId":"707eda45df73d980"},{"name":"handle-request","duration":835884,"timestamp":2507302436,"id":16,"tags":{"url":"/"},"startTime":1750306801290,"traceId":"707eda45df73d980"},{"name":"memory-usage","duration":9,"timestamp":2508138462,"id":20,"parentId":16,"tags":{"url":"/","memory.rss":"550629376","memory.heapUsed":"101675992","memory.heapTotal":"118878208"},"startTime":1750306802126,"traceId":"707eda45df73d980"},{"name":"compile-path","duration":4179132,"timestamp":2508937229,"id":23,"tags":{"trigger":"/favicon.ico"},"startTime":1750306802925,"traceId":"707eda45df73d980"}]
