{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///mnt/windows/vol1/ai_agent_code_review/app/ConvexClientProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ConvexProvider, ConvexReactClient } from \"convex/react\";\nimport { ReactNode } from \"react\";\n\nconst convex = new ConvexReactClient(process.env.NEXT_PUBLIC_CONVEX_URL!);\n\nexport function ConvexClientProvider({ children }: { children: ReactNode }) {\n  return <ConvexProvider client={convex}>{children}</ConvexProvider>;\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAFA;;;AAKA,MAAM,SAAS,IAAI,wJAAA,CAAA,oBAAiB;AAE7B,SAAS,qBAAqB,EAAE,QAAQ,EAA2B;IACxE,qBAAO,8OAAC,wJAAA,CAAA,iBAAc;QAAC,QAAQ;kBAAS;;;;;;AAC1C", "debugId": null}}]}